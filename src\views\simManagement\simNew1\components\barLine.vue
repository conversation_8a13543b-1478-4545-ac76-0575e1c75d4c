<template>
  <div>
    <div style="display: flex;width: 100%;height: 30px;">
      <div
        style="color: #fff;width: 50%;font-size: 18px;font-weight: bold;cursor: pointer;"
        @click="routeChange()"
      >{{title}}</div>
      <div style="width: 50%;display: flex;">
        <dayMonIndex
          :simlevel='simlevel'
          :position="Order"
          @showChack="getExcelbarline"
          :Dimension="Dimension"
          :Particle="Particle"
          :id1="id1"
          :BaseTime="BaseTime"
          :echarstType="4"
          :titlebarline="title"
          :backgroundImg="backgroundImg"
        />
      </div>
    </div>
    <div
      @click="openPopup()"
      :id="id1"
      style="width:100%;height:100%;"
    ></div>
    <!-- <div
      v-if="!showData"
      style="font-size: 14px;color: #fff;text-align: center;margin-top: -100px;"
    >暂无数据</div> -->
    <!-- <keyIndicatorslist
      ref="keyIndicatorsref"
      :exhibitionType="exhibitionType"
      :jtitle="jtitle"
    ></keyIndicatorslist> -->
    <keyIndicatorslistnew
      ref="keyIndicatorsrefnew"
      v-if="keyIndicatorslistnewShow"
      v-model="showkeyIndicatorslistnew"
      :exhibitionType="exhibitionType"
      :jtitle="title"
      :simlevel="simlevel"
      :Order="Order"
      :isSql="0"
      :BaseTime="BaseTime"
      :barName="barName"
      :backgroundImg="backgroundImg"
      @keynew="heandleKeybar"
    ></keyIndicatorslistnew>
  </div>
</template>
<script>
// import { getChartStructure } from '@/api/simConfig/simconfignew.js';
import { getqueryZ, getqueryLcr, getChartStructure } from '@/api/simConfig/simconfignew.js';

export default {
  components: {
    dayMonIndex: () => import('@/views/simManagement/simNew1/components/dayMonIndex.vue'),
    // keyIndicatorslist: () => import('@/views/simManagement/simNew1/components/keyIndicatorslist.vue'),
    keyIndicatorslistnew: () => import('@/views/simManagement/simNew1/components/keyIndicatorslistnew.vue'),
  },
  props: {
    id1: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    Order: {
      type: String,
      default: ''
    },
    configFlag: {
      type: String,
      default: ''
    },
    exhibitionType: {
      type: String,
      default: ''
    },
    simlevel: {
      type: String,
      default: ''
    },
    BaseTime: {
      type: String,
      default: ''
    },
    Dimension: {
      type: Array,
      default: () => []
    },
    routeList: {
      type: String,
      default: () => ''
    },
    backgroundImg: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    keyIndicatorslistnewShow: false,
    showkeyIndicatorslistnew: false,
    showData: true,
    Particle: '',
    jtitle: '',
    // BaseTime: '2024-07-01',
    // TeamCode: 'A1180240614',
    // ProductionLineCode: 'A11802406',
    // FactoryCode: 'A118024',
    curConfig: {},
    chartBarLine: null,
    styless: {
      width: '100%',
      height: '100%',
      backgroundImage: 'url("https://img1.baidu.com/it/u=2756664614,3290369440&fm=253&fmt=auto&app=138&f=JPEG?w=753&h=500")',
      backgroundSize: '100%',

    },
    //当前时间颗粒度
    curShift: {
      KpiValues: []
    },
    myShiftList: [],
    id: '',
    yAxisOption: {
      type: 'value',
      // show: false
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        show: true
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
  }),
  computed: {
    //x轴配置
    xAxisOption() {
      let list = ['7', '6', '5', '4', '3', '2', '1']

      // 检查是否需要2级x轴（包含规格信息）
      const hasSpecification = this.curShift?.GroupField &&
        this.curShift.GroupField.includes('SpecificationCode')

      if (this.curShift.ChartData && this.curShift.ChartData.x) {
        if (hasSpecification) {
          // 2级x轴配置：规格 + 日期
          return this.getTwoLevelXAxis()
        } else {
          // 原有的单级x轴配置
          list = this.curShift.ChartData.x.map(item => {
            // let month = dayjs(item).$M+1
            // let day = dayjs(item).$D
            if (['日', '周'].includes(this.curShift.TimeDimension)) {
              let month = item.split('-')[1]
              let day = item.split('-')[2]
              // 这里需要匹配颗粒度,来输出不同的x轴数据.
              // return `${month}-${day}`
              return `${month}-${day}`
            } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
              let label = item.split('年')[1]
              return label
            } else {
              return item
            }
          })
          // list.reverse()
          // list = this.curShift.ChartData.x
        }
      }

      return {
        type: 'category',
        // boundaryGap: false,
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    lineSeries() {
      if (this.curShift.ChartData.x.length <= 0) {
        this.$nextTick(() => {
          const dom = document.getElementById(this.id1);
          dom.innerHTML = '<div class="noDataBox">暂无数据</div>';
          dom.removeAttribute('_echarts_instance_');
          return
        })
      }
      //区分横竖
      let axisMarkLine = this.curConfig.ChartType === '4'
        ? [{ xAxis: this.curShift.TargetValue || '' }]
        : [{ yAxis: this.curShift.TargetValue || '' }]

      let list = []
      console.log(this.curConfig.ChartType, 'this.curConfig.ChartType');

      // 检查是否需要2级x轴处理
      const hasSpecification = this.curShift?.GroupField &&
        this.curShift.GroupField.includes('SpecificationCode')

      Object.keys(this.curShift.ChartData).forEach(key => {
        if (['x', 'x', '目标值'].includes(key)) {
          return
        }
        let obj = {
          // name: `${this.curShift.KpiName}实际值`,
          // name: key.split(':')[1],
          name: `${key}实际值`,
          type: ['2', '3'].includes(this.curConfig.ChartType) ? 'bar' : 'line',
          symbol: 'circle',
          symbolSize: 4,
          barWidth: 10,
          itemStyle: {
            normal: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#4391F4" // 0% 处的颜色
                }, {
                  offset: 1,
                  color: "#6B74E4" // 100% 处的颜色
                }], false),
              },
              barBorderRadius: [4, 4, 4, 4],
            }
          },
          // data: this.curShift.KpiValues.map(item=>item.DataValue),
          data: hasSpecification ?
            this.transformDataForTwoLevelAxis(key, this.curShift.ChartData[key]) :
            this.curShift.ChartData[key],
          markLine: {//目标值线条
            silent: true,
            lineStyle: {
              color: this.curShift.TargetColor || 'gray'
              // color: 'red'
            },
            data: axisMarkLine
            // data: [{xAxis: 20 }]
          }
        }
        list.push(obj)
      })
      return list
    },
  },
  created() {
    this.getBarList()
  },
  mounted() {

  },

  methods: {
    // 获取2级x轴配置
    getTwoLevelXAxis() {
      const chartData = this.curShift.ChartData
      const dates = chartData.x || []

      // 从ChartData中提取规格信息
      const specifications = new Set()

      // 遍历ChartData的键，提取规格信息
      Object.keys(chartData).forEach(key => {
        if (key !== 'x' && !key.includes('目标值') && !key.includes('上限') && !key.includes('下限')) {
          // 假设数据键格式为：规格_其他信息 或直接是规格
          const parts = key.split('_')
          if (parts.length > 1) {
            const spec = parts[0] // 取第一部分作为规格
            specifications.add(spec)
          }
        }
      })

      // 如果没有找到规格信息，回退到单级x轴
      if (specifications.size === 0) {
        return this.getSingleLevelXAxis()
      }

      // 构建2级x轴数据
      const specArray = Array.from(specifications)
      const combinedLabels = []

      // 为每个规格和日期组合创建标签
      specArray.forEach(spec => {
        dates.forEach(date => {
          const dateLabel = this.formatDateLabel(date)
          combinedLabels.push(`${spec}\n${dateLabel}`)
        })
      })

      return [
        {
          type: 'category',
          data: combinedLabels,
          axisLine: {
            onZero: false,
            lineStyle: {
              color: '#fff'
            },
          },
          axisLabel: {
            interval: 0,
            color: '#fff',
            fontSize: 10,
            lineHeight: 14
          },
          axisTick: {
            alignWithLabel: true
          }
        }
      ]
    },
    // 格式化日期标签
    formatDateLabel(dateStr) {
      if (['日', '周'].includes(this.curShift.TimeDimension)) {
        const parts = dateStr.split('-')
        if (parts.length >= 3) {
          return `${parts[1]}-${parts[2]}`
        }
      } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
        if (dateStr.includes('年')) {
          const parts = dateStr.split('年')
          return parts[0] + '-' + parts[1].split('月')[0]
        }
      }
      return dateStr
    },
    // 获取单级x轴配置（原有逻辑）
    getSingleLevelXAxis() {
      const list = this.curShift.ChartData.x.map(item => {
        if (['日', '周'].includes(this.curShift.TimeDimension)) {
          let month = item.split('-')[1]
          let day = item.split('-')[2]
          return `${month}-${day}`
        } else if (['月', '季度'].includes(this.curShift.TimeDimension)) {
          let label = item.split('年')[1]
          return label
        } else {
          return item
        }
      })

      return {
        type: 'category',
        axisLine: {
          onZero: false,
          lineStyle: {
            color: '#fff'
          },
        },
        axisLabel: {
          interval: 0,
          color: '#fff'
        },
        data: list
      }
    },
    // 转换数据以支持2级x轴
    transformDataForTwoLevelAxis(key, originalData) {
      if (!originalData || !this.curShift?.ChartData?.x) {
        return originalData
      }

      const dates = this.curShift.ChartData.x
      const specifications = new Set()

      // 从ChartData的键中提取规格信息
      Object.keys(this.curShift.ChartData).forEach(dataKey => {
        if (dataKey !== 'x' && !dataKey.includes('目标值') && !dataKey.includes('上限') && !dataKey.includes('下限')) {
          const parts = dataKey.split('_')
          if (parts.length > 1) {
            specifications.add(parts[0])
          }
        }
      })

      const specArray = Array.from(specifications)
      const transformedData = []

      // 为每个规格和日期组合创建数据点
      specArray.forEach(spec => {
        dates.forEach((date, dateIndex) => {
          // 查找对应规格和日期的数据
          const specKey = `${spec}_${key.split('_').slice(1).join('_')}`
          const dataValue = this.curShift.ChartData[specKey] ?
            this.curShift.ChartData[specKey][dateIndex] :
            (originalData[dateIndex] || 0)

          transformedData.push(dataValue)
        })
      })

      return transformedData
    },
    routeChange() {
      this.$router.push({ path: `${this.routeList}` })
      // this.$router.push({ path: 'simNew2', query: { code: item.Simcode } });
    },
    openPopup() {
      if (this.configFlag == '是') {
        this.$refs.keyIndicatorsref.showDialog = true;
      }
    },
    getExcelbarline(data) {
      this.Particle = data
      this.getBarList()
    },
    async getBarList() {
      let params = {
        "Position": this.Order,
        "BaseTime": this.BaseTime,
        "TeamCode": this.simlevel,
      }
      let { response } = await getChartStructure(params)
      this.curConfig = response
      if (this.curConfig?.ChartConfigs != null) {
        // this.id = this.curConfig.ID;
        // 每一个时间颗粒度默认取第一个节点的目标值为总目标值
        this.curConfig.ChartConfigs.map(item => {
          item.KpiName = this.curConfig.ChartConfigs.KpiName
          if (item.KpiValues[0]) {
            item.KpiCode = item.KpiValues[0].KpiCode
            item.TargetValue = item.KpiValues[0].TargetValue || 0
          }
        })
        //图表配置整体赋值
        // this.curConfig = response
        //时间颗粒度列表
        this.myShiftList = this.curConfig.ChartConfigs.filter(item => {
          return item.TargetVisible === 1
        })
        //默认激活第一个时间颗粒度
        if (this.Particle != '') {
          this.myShiftList.map((el, index) => {
            if (this.Particle == el.TimeDimension) {
              this.curShift = this.myShiftList[index]
              this.query1(true)
            } else {
              this.query1(false)
            }
          })
        } else {
          this.curShift = this.myShiftList[0]
          this.Particle = this.curShift.TimeDimension
          this.query1(true)
        }
      }
    },
    query(data) {
      this.showData = data
      if (data == false) {
        this.chartBarLine.clear()
        return
      }

      this.chartBarLine = this.$echarts.init(document.getElementById(this.id1));
      var option
      option = {
        symbol: 'circle',
        tooltip: {
          trigger: 'axis',
          extraCssText: 'z-index:999',
          axisPointer: {
            type: 'shadow',
          }
        },
        // legend: {
        //   data: this.lineLegend
        // },
        grid: {
          left: '5%',
          right: '6%',
          bottom: '3%',
          top: 20,
          containLabel: true
        },

        // visualMap: this.lineVisualMap,
        xAxis: this.xAxisOption,
        yAxis: {
          type: 'value',
          // show: false
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff" //X轴文字颜色
            },
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        // series: this.lineSeries
        series: [
          {
            name: '目标值',
            tooltip: {
              show: false
            },
            type: 'line',
            barWidth: 10,
            itemStyle: {
              normal: {
                barBorderRadius: [4, 4, 4, 4],
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#6B74E4" // 0% 处的颜色
                }, {
                  offset: 1,
                  color: "#6B74E4" // 100% 处的颜色
                }], false)
              }
            },
            // data: this.nianData.map(item => item.PLANVALUE),
            data: [85, 75, 80, 60, 68, 90],
            // data: this.list,
            barGap: '30%'

          },
          {
            name: '实际值',
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              normal: {
                barBorderRadius: [4, 4, 4, 4],
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#4391F4" // 0% 处的颜色
                }, {
                  offset: 1,
                  color: "#4391F4" // 100% 处的颜色
                }], false)
              }
            },
            // data: this.nianData.map(item => item.OUTNUM),
            data: [5, 20, 10, 18, 20, 20],
            barGap: '30%'
          },
        ]
      }
      this.chartBarLine.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBarLine.resize()
      }, false);
    },
    query1() {
      this.chartBarLine = this.$echarts.init(document.getElementById(this.id1));
      var option
      option = {
        title: {
          // text: this.title,
          textStyle: { // 标题样式
            color: '#fff'
          }
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          confine: true,
          extraCssText: 'max - width: none; overflow: visible;',
        },
        // legend: {
        //   right: 0,
        //   data: [
        //     {
        //       name: "目标值",
        //       textStyle: {
        //         color: "white"
        //       }
        //     },
        //     {
        //       name: "实际值",
        //       textStyle: {
        //         color: "white"
        //       }
        //     },
        //   ]
        // },
        grid: {
          top: '15%',
          bottom: '1%',
          right: '3%',
          left: '5%',
          containLabel: true
        },
        toolbox: {
          show: true,
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: "#fff"
              }
            },
            splitLine: {
              show: false
            },
            data: ['10.10', '10.11', '10.12'],//['10.10', '10.11', '10.12', '10.13', '10.14', '10.15'],
            // data: this.nianData.map(item => item.MONTH),
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
            },
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: "#fff"
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff" //X轴文字颜色
              },
            },
          },
          {
            //右边百分比部分
            name: '百分比',
            type: "value",
            position: "right",
            axisLine: {
              lineStyle: {
                color: "#fff"
              }
            },
            axisTick: {
              show: false,
            },

            axisLabel: {
              textStyle: {
                color: "#fff",
              },
              show: true,
              interval: "auto",
              formatter: "{value}%",
            },
            show: true,
            splitLine: {  //网格线
              show: false
            }
          }
        ],
        series: [

          {
            name: '目标值',
            type: 'bar',
            itemStyle: {
              normal: {
                barBorderRadius: [4, 4, 4, 4],
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#4391F4" // 0% 处的颜色
                }, {
                  offset: 1,
                  color: "#6B74E4" // 100% 处的颜色
                }], false)
              }
            },
            // data: this.nianData.map(item => item.PLANVALUE),
            data: [5, 20, 10],
            barGap: '30%'

          },
          {
            name: '实际值',
            yAxisIndex: 1,
            type: 'line',
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                  offset: 0,
                  color: "#4391F4" // 0% 处的颜色
                }, {
                  offset: 1,
                  color: "#6B74E4" // 100% 处的颜色
                }], false)
              }
            },
            // data: this.nianData.map(item => item.OUTNUM),

            data: [85, 75, 80],
            barGap: '30%',
            label: {
              formatter: '{c}%'
            }
          },
        ]
      };
      this.chartBarLine.setOption(option, true);
      window.addEventListener("resize", () => {
        this.chartBarLine.resize()
      }, false);
    }
  },
}
</script>
<style lang="scss" scoped>
::v-deep .noDataBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
}
</style>